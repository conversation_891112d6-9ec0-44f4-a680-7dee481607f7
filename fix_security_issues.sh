#!/bin/bash

# Telegram Bot Security Fix Script
# This script addresses the critical security issues found in the audit

set -e

echo "🔧 Starting security fixes for Telegram Bot..."

# Configuration
SSH_KEY="/Users/<USER>/Downloads/telegramleadtrackerkeypair.pem"
SERVER_HOST="ec2-13-38-130-31.eu-west-3.compute.amazonaws.com"
REGION="eu-west-3"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    if [[ ! -f "$SSH_KEY" ]]; then
        error "SSH key not found: $SSH_KEY"
        exit 1
    fi
    
    chmod 600 "$SSH_KEY"
    
    if ! command -v aws &> /dev/null; then
        error "AWS CLI not found. Please install it first."
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Fix SSM parameter names
fix_ssm_parameters() {
    log "Fixing SSM parameter names..."
    
    # Read current values from existing parameters
    API_ID=$(aws ssm get-parameter --region $REGION --name "/telegram-bot/api-id" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null || echo "")
    API_HASH=$(aws ssm get-parameter --region $REGION --name "/telegram-bot/api-hash" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null || echo "")
    CONTROL_BOT_TOKEN=$(aws ssm get-parameter --region $REGION --name "/telegram-bot/control-bot-token" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null || echo "")
    ADMIN_USER_ID=$(aws ssm get-parameter --region $REGION --name "/telegram-bot/admin-user-id" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null || echo "")
    ADMIN_CHAT_ID=$(aws ssm get-parameter --region $REGION --name "/telegram-bot/admin-chat-id" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null || echo "")
    GOOGLE_SHEET_NAME=$(aws ssm get-parameter --region $REGION --name "/telegram-bot/google-sheet-name" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null || echo "")
    
    # Create new parameters with correct names
    if [[ -n "$API_ID" ]]; then
        aws ssm put-parameter --region $REGION --name "/telegram-bot/API_ID" --value "$API_ID" --type "SecureString" --overwrite
        log "✓ Created /telegram-bot/API_ID"
    fi
    
    if [[ -n "$API_HASH" ]]; then
        aws ssm put-parameter --region $REGION --name "/telegram-bot/API_HASH" --value "$API_HASH" --type "SecureString" --overwrite
        log "✓ Created /telegram-bot/API_HASH"
    fi
    
    if [[ -n "$CONTROL_BOT_TOKEN" ]]; then
        aws ssm put-parameter --region $REGION --name "/telegram-bot/CONTROL_BOT_TOKEN" --value "$CONTROL_BOT_TOKEN" --type "SecureString" --overwrite
        log "✓ Created /telegram-bot/CONTROL_BOT_TOKEN"
    fi
    
    if [[ -n "$ADMIN_USER_ID" ]]; then
        aws ssm put-parameter --region $REGION --name "/telegram-bot/ADMIN_USER_ID" --value "$ADMIN_USER_ID" --type "SecureString" --overwrite
        log "✓ Created /telegram-bot/ADMIN_USER_ID"
    fi
    
    if [[ -n "$ADMIN_CHAT_ID" ]]; then
        aws ssm put-parameter --region $REGION --name "/telegram-bot/ADMIN_CHAT_ID" --value "$ADMIN_CHAT_ID" --type "SecureString" --overwrite
        log "✓ Created /telegram-bot/ADMIN_CHAT_ID"
    fi
    
    if [[ -n "$GOOGLE_SHEET_NAME" ]]; then
        aws ssm put-parameter --region $REGION --name "/telegram-bot/GOOGLE_SHEET_NAME" --value "$GOOGLE_SHEET_NAME" --type "SecureString" --overwrite
        log "✓ Created /telegram-bot/GOOGLE_SHEET_NAME"
    fi
}

# Add missing SSM parameters
add_missing_parameters() {
    log "Adding missing SSM parameters..."
    
    # Add AWS region
    aws ssm put-parameter --region $REGION --name "/telegram-bot/AWS_REGION" --value "$REGION" --type "String" --overwrite
    log "✓ Added AWS_REGION"
    
    # Add session encryption password (generate if not exists)
    ENCRYPTION_PASSWORD=$(openssl rand -base64 32)
    aws ssm put-parameter --region $REGION --name "/telegram-bot/SESSION_ENCRYPTION_PASSWORD" --value "$ENCRYPTION_PASSWORD" --type "SecureString" --overwrite
    log "✓ Added SESSION_ENCRYPTION_PASSWORD"
    
    # Add authorized user IDs
    aws ssm put-parameter --region $REGION --name "/telegram-bot/AUTHORIZED_USER_IDS" --value "7470338764,5118451874" --type "SecureString" --overwrite
    log "✓ Added AUTHORIZED_USER_IDS"
}

# Fix session file permissions
fix_session_permissions() {
    log "Fixing session file permissions..."
    
    ssh -i "$SSH_KEY" ubuntu@"$SERVER_HOST" "
        cd /opt/telegram-bot
        chmod 600 *.session 2>/dev/null || true
        ls -la *.session
    "
    
    log "✓ Session file permissions fixed"
}

# Remove hardcoded credentials from admin_bot.py
fix_hardcoded_credentials() {
    log "Fixing hardcoded credentials in admin_bot.py..."
    
    # Create a backup
    cp deployment/admin_bot.py deployment/admin_bot.py.backup
    
    # Replace hardcoded values with secrets_loader calls
    cat > deployment/admin_bot_fixed.py << 'EOF'
#!/usr/bin/env python3
"""
Telegram Bot Admin Panel - Secure Version
Remote administration interface for the Telegram lead tracker bot
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from telethon import TelegramClient, events, Button
from telethon.errors import SessionPasswordNeededError

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import secrets loader
try:
    from secrets_loader import get_secret
    
    # Load configuration from secrets
    API_ID = int(get_secret("API_ID"))
    API_HASH = get_secret("API_HASH")
    ADMIN_BOT_TOKEN = get_secret("CONTROL_BOT_TOKEN")
    ADMIN_USER_ID = int(get_secret("ADMIN_USER_ID"))
    GOOGLE_SHEET_NAME = get_secret("GOOGLE_SHEET_NAME")
    
except Exception as e:
    print(f"Failed to load secrets: {e}")
    sys.exit(1)

# Server configuration
SERVER_HOST = "ec2-13-38-130-31.eu-west-3.compute.amazonaws.com"
SSH_KEY = "/Users/<USER>/Downloads/telegramleadtrackerkeypair.pem"
SERVICE_NAME = "telegram-bot"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('admin_bot.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
EOF
    
    # Append the rest of the file (excluding the hardcoded credentials section)
    tail -n +40 deployment/admin_bot.py >> deployment/admin_bot_fixed.py
    
    # Replace the original file
    mv deployment/admin_bot_fixed.py deployment/admin_bot.py
    
    log "✓ Hardcoded credentials removed from admin_bot.py"
}

# Restart bot service to apply changes
restart_bot_service() {
    log "Restarting bot service..."
    
    ssh -i "$SSH_KEY" ubuntu@"$SERVER_HOST" "
        sudo systemctl restart telegram-bot
        sleep 5
        sudo systemctl status telegram-bot --no-pager
    "
    
    log "✓ Bot service restarted"
}

# Verify fixes
verify_fixes() {
    log "Verifying security fixes..."
    
    # Check SSM parameters
    log "Checking SSM parameters..."
    aws ssm describe-parameters --region $REGION --parameter-filters "Key=Name,Option=BeginsWith,Values=/telegram-bot/" --query 'Parameters[].Name' --output table
    
    # Check bot status
    log "Checking bot status..."
    ssh -i "$SSH_KEY" ubuntu@"$SERVER_HOST" "
        sudo systemctl is-active telegram-bot
        sudo journalctl -u telegram-bot --since '1 minute ago' --no-pager | tail -10
    "
    
    log "✓ Verification complete"
}

# Main execution
main() {
    log "Starting Telegram Bot Security Fix"
    
    check_prerequisites
    
    log "Phase 1: Fixing SSM parameters..."
    fix_ssm_parameters
    add_missing_parameters
    
    log "Phase 2: Fixing file permissions..."
    fix_session_permissions
    
    log "Phase 3: Removing hardcoded credentials..."
    fix_hardcoded_credentials
    
    log "Phase 4: Restarting services..."
    restart_bot_service
    
    log "Phase 5: Verification..."
    verify_fixes
    
    log "🎉 Security fixes completed successfully!"
    warn "Please review the SECURITY_AUDIT_REPORT.md for remaining items to address."
}

# Run main function
main "$@"
